<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AccuVelocity - Data Extraction Simplified</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #f5f5f5;
        }

        .login-container {
            display: flex;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .login-left {
            flex: 1;
            max-width: 500px;
            padding: 40px;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .login-right {
            flex: 1;
            background: linear-gradient(135deg, #e8f4f8 0%, #d1e7dd 100%);
            padding: 60px;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .login-right::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 600"><defs><linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23f0f8ff;stop-opacity:0.3" /><stop offset="100%" style="stop-color:%23e0f2f1;stop-opacity:0.1" /></linearGradient></defs><circle cx="350" cy="150" r="80" fill="url(%23grad)"/><circle cx="320" cy="300" r="60" fill="url(%23grad)"/><circle cx="380" cy="450" r="40" fill="url(%23grad)"/></svg>') no-repeat center;
            background-size: cover;
            opacity: 0.3;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 40px;
        }

        .logo-icon {
            font-size: 24px;
            color: #1e3a8a;
        }

        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #1e3a8a;
        }

        .form-tabs {
            display: flex;
            margin-bottom: 30px;
            border-radius: 8px;
            overflow: hidden;
            background: #f1f5f9;
        }

        .tab {
            flex: 1;
            padding: 12px 24px;
            border: none;
            background: transparent;
            color: #64748b;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab.active {
            background: #1e3a8a;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #e2e8f0;
        }

        .auth-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 500;
            color: #374151;
            font-size: 14px;
        }

        .form-group input {
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #1e3a8a;
            box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
        }

        .phone-input {
            display: flex;
            gap: 8px;
        }

        .country-code {
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            min-width: 80px;
        }

        .phone-input input {
            flex: 1;
        }

        .password-input {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: none;
            cursor: pointer;
            font-size: 16px;
        }

        .password-requirements {
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-top: 8px;
        }

        .requirement {
            font-size: 12px;
            color: #16a34a;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .checkbox-group {
            flex-direction: row;
            align-items: flex-start;
            gap: 12px;
        }

        .checkbox-label {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            line-height: 1.4;
        }

        .checkbox-label input[type="checkbox"] {
            margin: 0;
            width: 16px;
            height: 16px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .checkbox-label a {
            color: #1e3a8a;
            text-decoration: none;
        }

        .checkbox-label a:hover {
            text-decoration: underline;
        }

        .forgot-password {
            text-align: left;
            margin: -10px 0 10px 0;
        }

        .forgot-password a {
            color: #1e3a8a;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .submit-btn {
            padding: 14px 24px;
            background: #1e3a8a;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 10px;
        }

        .submit-btn:hover {
            background: #1e40af;
        }

        .marketing-content {
            position: relative;
            z-index: 1;
            max-width: 500px;
        }

        .marketing-content h2 {
            font-size: 32px;
            font-weight: 700;
            color: #1e3a8a;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .marketing-content h3 {
            font-size: 24px;
            font-weight: 600;
            color: #1e3a8a;
            margin-bottom: 24px;
            line-height: 1.2;
        }

        .marketing-content p {
            font-size: 16px;
            color: #4b5563;
            line-height: 1.6;
            margin-bottom: 32px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
            margin-bottom: 40px;
        }

        .stat {
            text-align: left;
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #1e3a8a;
            line-height: 1;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
            font-weight: 500;
        }

        .documents-section {
            margin-bottom: 40px;
        }

        .documents-section h4 {
            font-size: 18px;
            font-weight: 600;
            color: #1e3a8a;
            margin-bottom: 16px;
        }

        .documents-section ul {
            list-style: none;
            padding: 0;
        }

        .documents-section li {
            padding: 8px 0;
            color: #4b5563;
            font-size: 14px;
        }

        .social-section p {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 12px;
        }

        .social-links {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .social-links span {
            font-size: 14px;
            color: #6b7280;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: #1e3a8a;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }

        .social-link:hover {
            background: #1e40af;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
            }
            
            .login-left {
                max-width: none;
                padding: 20px;
            }
            
            .login-right {
                padding: 40px 20px;
            }
            
            .marketing-content h2 {
                font-size: 24px;
            }
            
            .marketing-content h3 {
                font-size: 20px;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }
            
            .stat-number {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-left">
            <div class="logo">
                <span class="logo-icon">⚡</span>
                <span class="logo-text">AccuVelocity</span>
            </div>

            <div class="form-tabs">
                <button class="tab active" onclick="showSignUp()">Sign Up</button>
                <button class="tab" onclick="showLogin()">Log In</button>
            </div>

            <!-- Sign Up Form -->
            <form id="signupForm" class="auth-form">
                <div class="form-group">
                    <label for="fullName">Full Name*</label>
                    <input type="text" id="fullName" name="fullName" placeholder="Your full name" required>
                </div>

                <div class="form-group">
                    <label for="phoneNumber">Phone number</label>
                    <div class="phone-input">
                        <select class="country-code">
                            <option value="+1">🇺🇸 +1</option>
                        </select>
                        <input type="tel" id="phoneNumber" name="phoneNumber">
                    </div>
                </div>

                <div class="form-group">
                    <label for="workEmail">Work Email*</label>
                    <input type="email" id="workEmail" name="workEmail" placeholder="<EMAIL>" required>
                </div>

                <div class="form-group">
                    <label for="password">Password*</label>
                    <div class="password-input">
                        <input type="password" id="password" name="password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">👁</button>
                    </div>
                    <div class="password-requirements">
                        <div class="requirement">✓ At least 8 characters</div>
                        <div class="requirement">✓ At least one uppercase letter</div>
                        <div class="requirement">✓ At least one number</div>
                        <div class="requirement">✓ At least one symbol</div>
                    </div>
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="isNotRobot">
                        I'm not a robot
                    </label>
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="agreeToTerms">
                        I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
                    </label>
                </div>

                <button type="submit" class="submit-btn">Create Account</button>
            </form>

            <!-- Login Form -->
            <form id="loginForm" class="auth-form hidden">
                <div class="form-group">
                    <label for="loginEmail">Work Email*</label>
                    <input type="email" id="loginEmail" name="workEmail" placeholder="<EMAIL>" required>
                </div>

                <div class="form-group">
                    <label for="loginPassword">Password*</label>
                    <div class="password-input">
                        <input type="password" id="loginPassword" name="password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('loginPassword')">👁</button>
                    </div>
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="isNotRobot">
                        I'm not a robot
                    </label>
                </div>

                <div class="forgot-password">
                    <a href="#">Forgot Password?</a>
                </div>

                <button type="submit" class="submit-btn">Login</button>
            </form>
        </div>

        <div class="login-right">
            <!-- Sign Up Marketing Content -->
            <div id="signupContent" class="marketing-content">
                <h2>Data Extraction Simplified:</h2>
                <h3>Fast - Accurate - Reliable</h3>
                
                <p>Stop struggling with data extraction manually. AccuVelocity automates the process, delivering fast, accurate, and reliable results. Get the clean data you need, effortlessly.</p>
                
                <div class="stats">
                    <div class="stat">
                        <div class="stat-number">80%</div>
                        <div class="stat-label">Efficiency</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">99%</div>
                        <div class="stat-label">Accuracy</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">4X</div>
                        <div class="stat-label">Scalability</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">70%</div>
                        <div class="stat-label">Time Savings</div>
                    </div>
                </div>

                <div class="social-section">
                    <p>For more information</p>
                    <div class="social-links">
                        <span>Follow Us on :</span>
                        <a href="#" class="social-link">📧</a>
                        <a href="#" class="social-link">🐦</a>
                        <a href="#" class="social-link">📘</a>
                        <a href="#" class="social-link">📷</a>
                        <a href="#" class="social-link">📺</a>
                    </div>
                </div>
            </div>

            <!-- Login Marketing Content -->
            <div id="loginContent" class="marketing-content hidden">
                <h2>Revolutionize Healthcare Data</h2>
                <h3>Management with AccuVelocity!</h3>
                
                <p>Take advantage of AccuVelocity's AI to transform raw healthcare data into actionable intelligence. Make data-driven decisions for improved patient outcomes.</p>
                
                <div class="documents-section">
                    <h4>Mostly Used Documents</h4>
                    <ul>
                        <li><strong>EOR:</strong> Explanation of Remittance</li>
                        <li><strong>EOB:</strong> Explanation of Benefits</li>
                        <li><strong>Reconsideration</strong></li>
                    </ul>
                </div>

                <div class="social-section">
                    <p>For more information</p>
                    <div class="social-links">
                        <span>Follow Us on :</span>
                        <a href="#" class="social-link">📧</a>
                        <a href="#" class="social-link">🐦</a>
                        <a href="#" class="social-link">📘</a>
                        <a href="#" class="social-link">📷</a>
                        <a href="#" class="social-link">📺</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSignUp() {
            // Update tabs
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab')[0].classList.add('active');
            
            // Show/hide forms
            document.getElementById('signupForm').classList.remove('hidden');
            document.getElementById('loginForm').classList.add('hidden');
            
            // Show/hide content
            document.getElementById('signupContent').classList.remove('hidden');
            document.getElementById('loginContent').classList.add('hidden');
        }

        function showLogin() {
            // Update tabs
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab')[1].classList.add('active');
            
            // Show/hide forms
            document.getElementById('signupForm').classList.add('hidden');
            document.getElementById('loginForm').classList.remove('hidden');
            
            // Show/hide content
            document.getElementById('signupContent').classList.add('hidden');
            document.getElementById('loginContent').classList.remove('hidden');
        }

        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
            input.setAttribute('type', type);
        }

        // Form submission handlers
        document.getElementById('signupForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Sign up form submitted! (This is a demo)');
        });

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Login form submitted! (This is a demo)');
        });
    </script>
</body>
</html>
