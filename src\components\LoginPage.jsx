import { useState } from 'react'
import './LoginPage.css'

const LoginPage = () => {
  const [isSignUp, setIsSignUp] = useState(true)
  const [formData, setFormData] = useState({
    fullName: '',
    phoneNumber: '',
    workEmail: '',
    password: '',
    agreeToTerms: false,
    isNotRobot: false
  })

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    console.log('Form submitted:', formData)
  }

  return (
    <div className="login-container">
      <div className="login-left">
        <div className="logo-section">
          <div className="logo">
            <span className="logo-icon">⚡</span>
            <span className="logo-text">AccuVelocity</span>
          </div>
        </div>

        <div className="form-section">
          <div className="form-tabs">
            <button 
              className={`tab ${isSignUp ? 'active' : ''}`}
              onClick={() => setIsSignUp(true)}
            >
              Sign Up
            </button>
            <button 
              className={`tab ${!isSignUp ? 'active' : ''}`}
              onClick={() => setIsSignUp(false)}
            >
              Log In
            </button>
          </div>

          <form onSubmit={handleSubmit} className="auth-form">
            {isSignUp ? (
              <>
                <div className="form-group">
                  <label htmlFor="fullName">Full Name*</label>
                  <input
                    type="text"
                    id="fullName"
                    name="fullName"
                    placeholder="Your full name"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="phoneNumber">Phone number</label>
                  <div className="phone-input">
                    <select className="country-code">
                      <option value="+1">🇺🇸 +1</option>
                    </select>
                    <input
                      type="tel"
                      id="phoneNumber"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="workEmail">Work Email*</label>
                  <input
                    type="email"
                    id="workEmail"
                    name="workEmail"
                    placeholder="<EMAIL>"
                    value={formData.workEmail}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="password">Password*</label>
                  <div className="password-input">
                    <input
                      type="password"
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                    />
                    <button type="button" className="password-toggle">👁</button>
                  </div>
                  <div className="password-requirements">
                    <div className="requirement">✓ At least 8 characters</div>
                    <div className="requirement">✓ At least one uppercase letter</div>
                    <div className="requirement">✓ At least one number</div>
                    <div className="requirement">✓ At least one symbol</div>
                  </div>
                </div>

                <div className="form-group checkbox-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="isNotRobot"
                      checked={formData.isNotRobot}
                      onChange={handleInputChange}
                    />
                    I'm not a robot
                  </label>
                </div>

                <div className="form-group checkbox-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="agreeToTerms"
                      checked={formData.agreeToTerms}
                      onChange={handleInputChange}
                    />
                    I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
                  </label>
                </div>

                <button type="submit" className="submit-btn">
                  Create Account
                </button>
              </>
            ) : (
              <>
                <div className="form-group">
                  <label htmlFor="workEmail">Work Email*</label>
                  <input
                    type="email"
                    id="workEmail"
                    name="workEmail"
                    placeholder="<EMAIL>"
                    value={formData.workEmail}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="password">Password*</label>
                  <div className="password-input">
                    <input
                      type="password"
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                    />
                    <button type="button" className="password-toggle">👁</button>
                  </div>
                </div>

                <div className="form-group checkbox-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="isNotRobot"
                      checked={formData.isNotRobot}
                      onChange={handleInputChange}
                    />
                    I'm not a robot
                  </label>
                </div>

                <div className="forgot-password">
                  <a href="#">Forgot Password?</a>
                </div>

                <button type="submit" className="submit-btn">
                  Login
                </button>
              </>
            )}
          </form>
        </div>
      </div>

      <div className="login-right">
        {isSignUp ? (
          <div className="marketing-content">
            <h2>Data Extraction Simplified:</h2>
            <h3>Fast - Accurate - Reliable</h3>
            
            <p>Stop struggling with data extraction manually. AccuVelocity automates the process, delivering fast, accurate, and reliable results. Get the clean data you need, effortlessly.</p>
            
            <div className="stats">
              <div className="stat">
                <div className="stat-number">80%</div>
                <div className="stat-label">Efficiency</div>
              </div>
              <div className="stat">
                <div className="stat-number">99%</div>
                <div className="stat-label">Accuracy</div>
              </div>
              <div className="stat">
                <div className="stat-number">4X</div>
                <div className="stat-label">Scalability</div>
              </div>
              <div className="stat">
                <div className="stat-number">70%</div>
                <div className="stat-label">Time Savings</div>
              </div>
            </div>

            <div className="social-section">
              <p>For more information</p>
              <div className="social-links">
                <span>Follow Us on :</span>
                <a href="#" className="social-link">📧</a>
                <a href="#" className="social-link">🐦</a>
                <a href="#" className="social-link">📘</a>
                <a href="#" className="social-link">📷</a>
                <a href="#" className="social-link">📺</a>
              </div>
            </div>
          </div>
        ) : (
          <div className="marketing-content">
            <h2>Revolutionize Healthcare Data</h2>
            <h3>Management with AccuVelocity!</h3>
            
            <p>Take advantage of AccuVelocity's AI to transform raw healthcare data into actionable intelligence. Make data-driven decisions for improved patient outcomes.</p>
            
            <div className="documents-section">
              <h4>Mostly Used Documents</h4>
              <ul>
                <li><strong>EOR:</strong> Explanation of Remittance</li>
                <li><strong>EOB:</strong> Explanation of Benefits</li>
                <li><strong>Reconsideration</strong></li>
              </ul>
            </div>

            <div className="social-section">
              <p>For more information</p>
              <div className="social-links">
                <span>Follow Us on :</span>
                <a href="#" className="social-link">📧</a>
                <a href="#" className="social-link">🐦</a>
                <a href="#" className="social-link">📘</a>
                <a href="#" className="social-link">📷</a>
                <a href="#" className="social-link">📺</a>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default LoginPage
