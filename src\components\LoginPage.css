.login-container {
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;
}

.login-left {
  flex: 1;
  max-width: 500px;
  padding: 40px;
  background: white;
  display: flex;
  flex-direction: column;
}

.login-right {
  flex: 1;
  background: linear-gradient(135deg, #e8f4f8 0%, #d1e7dd 100%);
  padding: 60px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.login-right::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 600"><defs><linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23f0f8ff;stop-opacity:0.3" /><stop offset="100%" style="stop-color:%23e0f2f1;stop-opacity:0.1" /></linearGradient></defs><circle cx="350" cy="150" r="80" fill="url(%23grad)"/><circle cx="320" cy="300" r="60" fill="url(%23grad)"/><circle cx="380" cy="450" r="40" fill="url(%23grad)"/></svg>') no-repeat center;
  background-size: cover;
  opacity: 0.3;
}

/* Logo Section */
.logo-section {
  margin-bottom: 40px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 24px;
  color: #1e3a8a;
}

.logo-text {
  font-size: 24px;
  font-weight: 700;
  color: #1e3a8a;
}

/* Form Tabs */
.form-tabs {
  display: flex;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  background: #f1f5f9;
}

.tab {
  flex: 1;
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: #64748b;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab.active {
  background: #1e3a8a;
  color: white;
}

.tab:hover:not(.active) {
  background: #e2e8f0;
}

/* Form Styles */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-group input {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #1e3a8a;
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* Phone Input */
.phone-input {
  display: flex;
  gap: 8px;
}

.country-code {
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  min-width: 80px;
}

.phone-input input {
  flex: 1;
}

/* Password Input */
.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background: none;
  cursor: pointer;
  font-size: 16px;
}

/* Password Requirements */
.password-requirements {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 8px;
}

.requirement {
  font-size: 12px;
  color: #16a34a;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Checkbox Groups */
.checkbox-group {
  flex-direction: row;
  align-items: flex-start;
  gap: 12px;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-label a {
  color: #1e3a8a;
  text-decoration: none;
}

.checkbox-label a:hover {
  text-decoration: underline;
}

/* Forgot Password */
.forgot-password {
  text-align: left;
  margin: -10px 0 10px 0;
}

.forgot-password a {
  color: #1e3a8a;
  text-decoration: none;
  font-size: 14px;
}

.forgot-password a:hover {
  text-decoration: underline;
}

/* Submit Button */
.submit-btn {
  padding: 14px 24px;
  background: #1e3a8a;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 10px;
}

.submit-btn:hover {
  background: #1e40af;
}

/* Marketing Content */
.marketing-content {
  position: relative;
  z-index: 1;
  max-width: 500px;
}

.marketing-content h2 {
  font-size: 32px;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 8px;
  line-height: 1.2;
}

.marketing-content h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e3a8a;
  margin-bottom: 24px;
  line-height: 1.2;
}

.marketing-content p {
  font-size: 16px;
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 32px;
}

/* Stats Section */
.stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 40px;
}

.stat {
  text-align: left;
}

.stat-number {
  font-size: 36px;
  font-weight: 700;
  color: #1e3a8a;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Documents Section */
.documents-section {
  margin-bottom: 40px;
}

.documents-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1e3a8a;
  margin-bottom: 16px;
}

.documents-section ul {
  list-style: none;
  padding: 0;
}

.documents-section li {
  padding: 8px 0;
  color: #4b5563;
  font-size: 14px;
}

/* Social Section */
.social-section p {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 12px;
}

.social-links {
  display: flex;
  align-items: center;
  gap: 12px;
}

.social-links span {
  font-size: 14px;
  color: #6b7280;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #1e3a8a;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.social-link:hover {
  background: #1e40af;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }
  
  .login-left {
    max-width: none;
    padding: 20px;
  }
  
  .login-right {
    padding: 40px 20px;
  }
  
  .marketing-content h2 {
    font-size: 24px;
  }
  
  .marketing-content h3 {
    font-size: 20px;
  }
  
  .stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .stat-number {
    font-size: 28px;
  }
}
